import express from "express";
import {
  createBook,
  getBooks,
  getBook,
  updateBook,
  deleteBook,
} from "../controllers/book.js";
import {
  createBookValidator,
  updateBookValidator,
} from "../validators/book.validator.js";
import upload from "../config/multer.js";

const router = express.Router();

router.post("/", upload.single('image'), createBookValidator, createBook);
router.get("/", getBooks);
router.get("/:id", getBook);
router.put("/:id", upload.single('image'), updateBookValidator, updateBook);
router.delete("/:id", deleteBook);

export default router;
