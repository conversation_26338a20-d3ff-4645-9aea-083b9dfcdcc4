<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>BYJSM BookStore - Checkout</title>

    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Poppins:wght@300;400;500;600&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="/css/main.css" />
    <style>
      body {
        padding-top: 120px; /* Base padding for navbar + margin */
      }

      @media (max-width: 991.98px) {
        body {
          padding-top: 100px; /* Slightly less padding on mobile */
        }
      }

      .card-header {
        background: var(--gradient-primary) !important;
      }
      
      .btn-primary {
        background: var(--gradient-primary);
        border: none;
      }
      
      .btn-primary:hover {
        background: var(--gradient-secondary);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(90, 0, 23, 0.2);
      }

      .text-primary {
        color: var(--primary-color) !important;
      }

      .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
      }

      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
      }

      .form-control:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.2rem rgba(122, 28, 42, 0.15);
      }

      .form-label {
        color: var(--text-color);
        font-weight: 500;
      }

      /* Credit Card Styles */
      .credit-card-container {
        perspective: 1000px;
        width: 100%;
        max-width: 400px;
        margin: 20px auto;
      }

      .credit-card {
        position: relative;
        width: 100%;
        height: 220px;
        transition: transform 0.6s;
        transform-style: preserve-3d;
      }

      .credit-card.flipped {
        transform: rotateY(180deg);
      }

      .card-front, .card-back {
        position: absolute;
        width: 100%;
        height: 100%;
        backface-visibility: hidden;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      }

      .card-front {
        background: linear-gradient(45deg, #1a1a1a, #4a4a4a);
        color: white;
        z-index: 2;
      }

      .card-back {
        background: linear-gradient(45deg, #2a2a2a, #5a5a5a);
        transform: rotateY(180deg);
        color: white;
      }

      .card-logo {
        text-align: right;
        margin-bottom: 20px;
      }

      .card-number {
        font-size: 1.5em;
        letter-spacing: 2px;
        margin: 20px 0;
        font-family: monospace;
      }

      .card-details {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
      }

      .card-holder {
        font-size: 0.9em;
        text-transform: uppercase;
      }

      .card-expiry {
        font-size: 0.9em;
      }

      .card-stripe {
        background: #000;
        height: 40px;
        margin: 20px 0;
      }

      .card-signature {
        background: #fff;
        height: 40px;
        margin-top: 20px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        color: #000;
      }

      .card-signature::before {
        content: "CVV";
        font-size: 0.8em;
        margin-right: 10px;
      }
    </style>
  </head>
  <body>
    <%- include('partials/navbar') %>

    <div class="container py-5">
      <div class="row">
        <!-- Order Summary -->
        <div class="col-lg-4 order-lg-2 mb-4">
          <div class="card">
            <div class="card-header text-white">
              <h5 class="mb-0">Order Summary</h5>
            </div>
            <div class="card-body">
              <% if (user && user.cart.items.length > 0) { %> <%
              user.cart.items.forEach(item => { %>
              <div
                class="d-flex justify-content-between align-items-center mb-3"
              >
                <div>
                  <h6 class="mb-0"><%= item.book.title %></h6>
                  <small class="text-muted"
                    >Quantity: <%= item.quantity %></small
                  >
                </div>
                <span
                  >EGP <%= (item.book.price * item.quantity).toFixed(2) %></span
                >
              </div>
              <% }); %>
              <hr />
              <div class="d-flex justify-content-between mb-2">
                <span>Subtotal:</span>
                <span>EGP <%= user.cart.totalPrice.toFixed(2) %></span>
              </div>
              <div class="d-flex justify-content-between mb-2">
                <span>Shipping:</span>
                <span>EGP 5.00</span>
              </div>
              <hr />
              <div class="d-flex justify-content-between mb-3">
                <strong>Total:</strong>
                <strong class="text-primary"
                  >EGP <%= (user.cart.totalPrice + 5).toFixed(2) %></strong
                >
              </div>
              <% } else { %>
              <p class="text-muted">Your cart is empty.</p>
              <% } %>
            </div>
          </div>
        </div>

        <!-- Checkout Form -->
        <div class="col-lg-8 order-lg-1">
          <div class="card">
            <div class="card-header text-white">
              <h5 class="mb-0">Checkout Information</h5>
            </div>
            <div class="card-body">
              <form id="checkoutForm">
                <!-- Shipping Information -->
                <h6 class="mb-3">Shipping Information</h6>
                <div class="row g-3 mb-4">
                  <div class="col-md-6">
                    <label class="form-label">Full Name</label>
                    <input
                      type="text"
                      class="form-control"
                      name="fullName"
                      required
                    />
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Email</label>
                    <input
                      type="email"
                      class="form-control"
                      name="email"
                      value="<%= user.email %>"
                      required
                    />
                  </div>
                  <div class="col-12">
                    <label class="form-label">Address</label>
                    <input
                      type="text"
                      class="form-control"
                      name="address"
                      required
                    />
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">City</label>
                    <input
                      type="text"
                      class="form-control"
                      name="city"
                      required
                    />
                  </div>
                  <div class="col-md-3">
                    <label class="form-label">State</label>
                    <input
                      type="text"
                      class="form-control"
                      name="state"
                      required
                    />
                  </div>
                  <div class="col-md-3">
                    <label class="form-label">ZIP Code</label>
                    <input
                      type="text"
                      class="form-control"
                      name="zipCode"
                      required
                    />
                  </div>
                </div>

                <!-- Payment Information -->
                <div id="paymentSection">
                  <h6 class="mb-3" id="paymentHeader" style="display: none;">Payment Information</h6>
                  <div class="row g-3 mb-4">
                    <div class="col-12">
                      <label class="form-label">Payment Method</label>
                      <select class="form-select" name="paymentMethod" id="paymentMethod" required>
                        <option value="">Select Payment Method</option>
                        <option value="card">Credit/Debit Card</option>
                        <option value="cod">Cash on Delivery</option>
                      </select>
                    </div>

                    <!-- Card Payment Fields -->
                    <div id="cardPaymentFields" class="row g-3" style="display: none;">
                      <div class="col-12">
                        <div class="credit-card-container">
                          <div class="credit-card" id="creditCard">
                            <div class="card-front">
                              <div class="card-logo">
                                <i class="fab fa-cc-visa fa-2x"></i>
                              </div>
                              <div class="card-number" id="cardNumberDisplay">**** **** **** ****</div>
                              <div class="card-details">
                                <div class="card-holder" id="cardHolderDisplay">CARD HOLDER</div>
                                <div class="card-expiry" id="cardExpiryDisplay">MM/YY</div>
                              </div>
                            </div>
                            <div class="card-back">
                              <div class="card-stripe"></div>
                              <div class="card-signature">
                                <span id="cardCvvDisplay">***</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-12">
                        <label class="form-label">Card Number</label>
                        <input
                          type="text"
                          class="form-control"
                          name="cardNumber"
                          id="cardNumber"
                          maxlength="19"
                          placeholder="1234 5678 9012 3456"
                        />
                      </div>
                      <div class="col-md-6">
                        <label class="form-label">Card Holder Name</label>
                        <input
                          type="text"
                          class="form-control"
                          name="cardHolder"
                          id="cardHolder"
                          placeholder="JOHN DOE"
                        />
                      </div>
                      <div class="col-md-3">
                        <label class="form-label">Expiration Date</label>
                        <input
                          type="text"
                          class="form-control"
                          name="expDate"
                          id="expDate"
                          placeholder="MM/YY"
                          maxlength="5"
                        />
                      </div>
                      <div class="col-md-3">
                        <label class="form-label">CVV</label>
                        <input
                          type="text"
                          class="form-control"
                          name="cvv"
                          id="cvv"
                          maxlength="3"
                          placeholder="123"
                        />
                      </div>
                    </div>

                    <!-- Cash on Delivery Message -->
                    <div id="codMessage" class="col-12" style="display: none;">
                      <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        You will pay the total amount when your order is delivered.
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  type="submit"
                  class="btn btn-primary w-100"
                  id="placeOrderBtn"
                >
                  <i class="fas fa-lock me-2"></i>Place Order
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <%- include('partials/footer') %>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>

    <script>
      // Function to adjust body padding based on navbar height
      function adjustBodyPadding() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
          const navbarHeight = navbar.offsetHeight;
          const navbarMargin = parseInt(window.getComputedStyle(navbar).marginTop);
          document.body.style.paddingTop = `${navbarHeight + navbarMargin}px`;
        }
      }

      // Run on load and resize
      window.addEventListener('load', adjustBodyPadding);
      window.addEventListener('resize', adjustBodyPadding);

      // Credit Card Interaction
      const creditCard = document.getElementById('creditCard');
      const cardNumber = document.getElementById('cardNumber');
      const cardHolder = document.getElementById('cardHolder');
      const expDate = document.getElementById('expDate');
      const cvv = document.getElementById('cvv');

      // Format card number with spaces
      cardNumber.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
        let formattedValue = '';
        for(let i = 0; i < value.length; i++) {
          if(i > 0 && i % 4 === 0) {
            formattedValue += ' ';
          }
          formattedValue += value[i];
        }
        e.target.value = formattedValue;
        document.getElementById('cardNumberDisplay').textContent = formattedValue || '**** **** **** ****';
      });

      // Update card holder name
      cardHolder.addEventListener('input', function(e) {
        document.getElementById('cardHolderDisplay').textContent = e.target.value.toUpperCase() || 'CARD HOLDER';
      });

      // Format and update expiry date
      expDate.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
        if (value.length >= 2) {
          value = value.slice(0,2) + '/' + value.slice(2);
        }
        e.target.value = value;
        document.getElementById('cardExpiryDisplay').textContent = value || 'MM/YY';
      });

      // Update CVV and flip card
      cvv.addEventListener('input', function(e) {
        let value = e.target.value.replace(/[^0-9]/gi, '');
        e.target.value = value;
        document.getElementById('cardCvvDisplay').textContent = value || '***';
      });

      // Flip card on CVV focus/blur
      cvv.addEventListener('focus', function() {
        creditCard.classList.add('flipped');
      });

      cvv.addEventListener('blur', function() {
        creditCard.classList.remove('flipped');
      });

      // Payment method toggle
      document.getElementById('paymentMethod').addEventListener('change', function() {
        const cardFields = document.getElementById('cardPaymentFields');
        const codMessage = document.getElementById('codMessage');
        const paymentHeader = document.getElementById('paymentHeader');
        
        if (this.value === 'card') {
          cardFields.style.display = 'flex';
          codMessage.style.display = 'none';
          paymentHeader.style.display = 'block';
          // Make card fields required
          cardFields.querySelectorAll('input').forEach(input => {
            input.required = true;
          });
        } else if (this.value === 'cod') {
          cardFields.style.display = 'none';
          codMessage.style.display = 'block';
          paymentHeader.style.display = 'none';
          // Remove required from card fields
          cardFields.querySelectorAll('input').forEach(input => {
            input.required = false;
          });
        } else {
          cardFields.style.display = 'none';
          codMessage.style.display = 'none';
          paymentHeader.style.display = 'none';
        }
      });

      document
        .getElementById("checkoutForm")
        .addEventListener("submit", async (e) => {
          e.preventDefault();
          const btn = document.getElementById("placeOrderBtn");
          btn.disabled = true;
          btn.innerHTML =
            '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';

          try {
            const response = await fetch("/api/order", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              credentials: "include",
            });

            const data = await response.json();

            if (response.ok) {
              alert("Order placed successfully!");
              window.location.href = "/profile";
            } else {
              alert(
                data.message ||
                  (data.errors && data.errors[0]?.msg) ||
                  "Order failed"
              );
            }
          } catch (err) {
            alert("An error occurred while placing the order.");
          } finally {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-lock me-2"></i>Place Order';
          }
        });
    </script>
  </body>
</html>
